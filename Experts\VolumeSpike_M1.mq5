//+------------------------------------------------------------------+
//|                                                  VolumeSpike.mq5 |
//|                        Copyright 2023, MetaQuotes Ltd.           |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2023, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "1.00"

input int    WindowSize = 100;       // 基准成交量计算窗口
input double Threshold = 300.0;       // 突增阈值(%)
input int    MinBaseVolume = 10;      // 最小基准成交量过滤
input int    ConsecutiveTicks = 3;    // 突增持续Tick数
input double PriceChangeFilter = 1.0;// 价格变动过滤(%)（显示为点数）

// 日志缓冲区大小常量
#define LOG_BUFFER_SIZE 100

// 环形缓冲区数据结构
struct RingBuffer
{
    double   volumes[];
    double   prices[];
    long     sum;
    double   sum_sq;
    int      index;
    int      count;
};

// 日志缓冲区数据结构
struct LogBuffer
{
    string   messages[];
    datetime timestamps[];
    int      count;
    datetime lastPanelUpdate;
};

// 异步打印函数
void AsyncPrint(string message)
{
    Print(message);
}

RingBuffer buf;
LogBuffer  logBuf;         // 日志缓冲区
bool       spikeFlags[3];  // 存储最近3个Tick的标志
MqlTick    lastTick;

//+------------------------------------------------------------------+
//| 初始化环形缓冲区                                                 |
//+------------------------------------------------------------------+
void InitializeBuffer()
{
    ArrayResize(buf.volumes, WindowSize);
    ArrayResize(buf.prices, WindowSize);
    ArrayInitialize(buf.volumes, 0);
    ArrayInitialize(buf.prices, 0);
    
    // 正确初始化统计值
    buf.sum = 0;
    buf.sum_sq = 0;
    buf.count = 0;
    buf.index = 0;
    
    // 使用iVolume函数加载历史成交量数据
    datetime currentTime = TimeCurrent();
    int validCount = 0;
    
    // 从最近的历史数据开始加载
    for(int i = 0; i < WindowSize; i++) {
        datetime barTime = currentTime - PeriodSeconds(PERIOD_M1) * i;
        long volume = iVolume(_Symbol, PERIOD_M1, i);
        double price = iClose(_Symbol, PERIOD_M1, i);
        
        // 验证数据有效性
        if(volume > 0 && price > 0) {
            buf.volumes[validCount] = (double)volume;
            buf.prices[validCount] = price;
            buf.sum += volume;
            buf.sum_sq += volume * volume;
            validCount++;
        }
    }
    
    buf.count = validCount;
    buf.index = validCount % WindowSize;
    
    if(validCount > 0) {
        Print(StringFormat("成功加载 %d 个有效的历史成交量数据", validCount));
    } else {
        Print("警告：未找到有效的历史成交量数据，请确保市场数据可用");
    }
}

//+------------------------------------------------------------------+
//| 更新缓冲区并返回是否满足突增条件                                 |
//+------------------------------------------------------------------+
bool UpdateBuffer(const MqlTick &tick)
{
    // 移出旧数据
    if(buf.count >= WindowSize && buf.count > 0)
    {
        double oldVol = buf.volumes[buf.index];
        buf.sum -= oldVol;
        buf.sum_sq -= oldVol * oldVol;
    }

    // 添加新数据
    double volume = tick.volume;
    double price = tick.last;
    
    // 增强的数据有效性验证
    if(volume <= 0) {
        // Print(StringFormat("跳过无效成交量数据：%.2f", volume));
        return false;
    }
    
    if(price <= 0) {
        // Print(StringFormat("跳过无效价格数据：%.5f", price));
        return false;
    }
    
    // 添加有效数据
    buf.volumes[buf.index] = volume;
    buf.prices[buf.index] = price;
    buf.sum += volume;
    buf.sum_sq += volume * volume;
    
    // 调试输出
    // Print(StringFormat("成功添加新数据 - 成交量: %.2f, 价格: %.5f", volume, price));

    // 更新索引
    buf.index = (buf.index + 1) % WindowSize;
    if(buf.count < WindowSize) buf.count++;

    // 计算统计指标
    double mean = buf.sum / (double)buf.count;
    // 添加缓冲区状态验证
    if(buf.count < 2) {
        Print("统计计算跳过：缓冲区计数 ", buf.count);
        return false;
    }
    
    double variance = (buf.sum_sq - (buf.sum * buf.sum)/buf.count) / (buf.count - 1);
    double stddev = sqrt(fmax(variance, 0));
    double zscore = (stddev > 0) ? (tick.volume - mean) / stddev : 0;

    // 基准成交量过滤
    if(mean < MinBaseVolume) return false;

    // 突增比率计算
    double spikeRatio = (tick.volume / mean) * 100.0;

    // 价格变动计算
    double priceChange = 0.0;
    double priceDelta = 0.0;
    // 使用缓冲区中的历史价格数据计算价格变动，与UpdateInfoPanel保持一致
    if(buf.count > 1 && buf.prices[(buf.index-2+WindowSize)%WindowSize] > 0) {
        double prevPrice = buf.prices[(buf.index-2+WindowSize)%WindowSize];
        priceDelta = tick.last - prevPrice;
        priceChange = (priceDelta / prevPrice) * 100.0;  // 百分比变动用于突增判断
    }

    // 更新突增标志队列
    bool currentSpike = (spikeRatio >= Threshold) && 
                       (zscore > 3) && 
                       (MathAbs(priceChange) >= PriceChangeFilter);
                       
    // 维护持续检测窗口
    for(int i=ConsecutiveTicks-1; i>0; i--)
        spikeFlags[i] = spikeFlags[i-1];
    spikeFlags[0] = currentSpike;

    // 检查是否满足持续条件
    bool validSpike = true;
    for(int i=0; i<ConsecutiveTicks; i++)
    {
        if(!spikeFlags[i])
        {
            validSpike = false;
            break;
        }
    }
    
    return validSpike;
}

//+------------------------------------------------------------------+
//| 主Tick处理函数                                                   |
//+------------------------------------------------------------------+
void OnTick()
{
    MqlTick currentTick;
    if(!SymbolInfoTick(_Symbol, currentTick)) {
        Print("无法获取当前行情数据，错误码：", GetLastError());
        return;
    }
    
    // 使用iVolume获取当前K线成交量作为备选数据源
    long currentVolume = iVolume(_Symbol, PERIOD_M1, 0);
    
    // 验证成交量数据有效性
    if(currentTick.volume <= 0 && currentVolume <= 0) {
        static datetime lastWarning = 0;
        datetime currentTime = TimeCurrent();
        
        // 每分钟最多输出一次警告
        if(currentTime - lastWarning >= 60) {
            Print("警告：检测到无效的成交量数据，Tick成交量：", currentTick.volume, "，K线成交量：", currentVolume);
            lastWarning = currentTime;
        }
        return;
    }
    
    // 如果Tick成交量无效，使用K线成交量
    if(currentTick.volume <= 0) {
        currentTick.volume = (double)currentVolume;
    }
    
    // 跳过相同时间的Tick
    if(currentTick.time == lastTick.time && 
       currentTick.volume == lastTick.volume) return;
       
    bool isSpike = UpdateBuffer(currentTick);
    
    // 仅在缓冲区有效时更新面板
    if(buf.count >= 2) {
        UpdateInfoPanel(currentTick);
    }
    
    if(isSpike)
    {
        // 获取价格变动点数
        double priceDelta = 0.0;
        if(buf.count > 1 && buf.prices[(buf.index-2+WindowSize)%WindowSize] > 0) {
            double prevPrice = buf.prices[(buf.index-2+WindowSize)%WindowSize];
            priceDelta = currentTick.last - prevPrice;
        }
        
        string msg = StringFormat("成交量突增事件于 %s\n当前价格: %.5f\n价格变动: %.1f点\n成交量: %.2f",
                          TimeToString(currentTick.time), 
                          currentTick.last, 
                          priceDelta * 10000, // 转换为点数显示
                          currentTick.volume);
        Alert(msg);
        Print(msg);
    }
    
    lastTick = currentTick;
}

//+------------------------------------------------------------------+
//| 初始化函数                                                       |
//+------------------------------------------------------------------+
int OnInit()
{
    // 设置图表属性以确保面板可见
    ChartSetInteger(0, CHART_FOREGROUND, false);
    ChartSetInteger(0, CHART_SHIFT, true);
    ChartSetInteger(0, CHART_SHOW_OBJECT_DESCR, true);
    
    // 初始化数据和面板
    InitializeBuffer();
    ArrayInitialize(spikeFlags, false);
    
    // 初始化日志缓冲区
    ArrayResize(logBuf.messages, LOG_BUFFER_SIZE);
    ArrayResize(logBuf.timestamps, LOG_BUFFER_SIZE);
    logBuf.count = 0;
    logBuf.lastPanelUpdate = 0;
    
    // 确保面板被创建并可见
    CreateInfoPanel();
    
    // 强制重绘图表
    ChartRedraw(0);
    
    AsyncPrint("VolumeSpike EA 初始化完成，信息面板已创建");
    return(INIT_SUCCEEDED);
}

// 信息面板相关变量
string infoPanel;

//+------------------------------------------------------------------+
//| 创建信息面板                                                     |
//+------------------------------------------------------------------+
void CreateInfoPanel()
{
    // 删除可能存在的旧对象
    ObjectDelete(0, infoPanel);
    
    // 直接使用Comment函数显示信息
    Comment("等待数据...");
    Print("信息面板初始化完成");
}

//+------------------------------------------------------------------+
//| 统一数据验证函数                                                 |
//+------------------------------------------------------------------+
bool ValidateData(const double value, const string type)
{
    if(value <= 0) {
        Print(type + "数据无效：", value);
        return false;
    }
    return true;
}

//+------------------------------------------------------------------+
//| 更新信息面板                                                     |
//+------------------------------------------------------------------+
void UpdateInfoPanel(const MqlTick &tick)
{
    if(buf.count < 2) {
        Comment("数据不足 (需要至少2个数据点)");
        return;
    }
    
    // 计算关键指标
    double mean = buf.sum / (double)buf.count;
    double variance = (buf.sum_sq - (buf.sum * buf.sum)/buf.count) / (buf.count - 1);
    double stddev = sqrt(fmax(variance, 0));
    double zscore = (stddev > 0) ? (tick.volume - mean) / stddev : 0;
    double spikeRatio = (tick.volume / mean) * 100.0;
    
    // 计算价格变动
    double priceChange = 0.0;  // 百分比变动（用于突增判断）
    double priceDelta = 0.0;   // 点数变动（用于显示）
    if(buf.count > 1 && buf.prices[(buf.index-2+WindowSize)%WindowSize] > 0) {
        double prevPrice = buf.prices[(buf.index-2+WindowSize)%WindowSize];
        priceDelta = tick.last - prevPrice;
        priceChange = (priceDelta / prevPrice) * 100.0;  // 仍然计算百分比用于突增判断
    }
    
    // 判断是否处于突增状态
    bool isCurrentSpike = (spikeRatio >= Threshold) && 
                         (zscore > 3) && 
                         (MathAbs(priceChange) >= PriceChangeFilter);
    
    // 构建显示文本
    string spikeStatus = isCurrentSpike ? "\n*** 成交量突增警报 ***" : "";
    string text = StringFormat("当前成交量: %.2f\n平均成交量: %.2f\nZ值: %.2f\n突增比率: %.1f%%\n价格变动: %.1f点 (%.2f%%)%s",
                              tick.volume,
                              mean,
                              zscore,
                              spikeRatio,
                              priceDelta * 10000, // 转换为点数显示（1点=0.0001）
                              priceChange,
                              spikeStatus);

    Comment(text);
}

//+------------------------------------------------------------------+
//| 逆初始化函数                                                     |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    Comment(""); // 清除注释
    Print("EA 已卸载");
}