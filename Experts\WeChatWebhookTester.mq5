//+------------------------------------------------------------------+
//|                                                  WeChatWebhookTester.mq5 |
//|                                    Generated by ChatGPT/DeepSeek |
//|                                         https://www.deepseek.com |
//+------------------------------------------------------------------+
#property copyright "ChatGPT/DeepSeek"
#property version   "1.00"
#property description "WeChat Webhook URL Tester"

#include <..\Include\WeChatRobotManager.mqh>

//--- 输入参数
input string   WeChat_Webhook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=c48b29b1-0dbb-41e7-be9b-d1d123f5f110"; // 微信机器人Webhook地址

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   CWeChatRobotManager wechatManager;
   if(!wechatManager.Initialize(WeChat_Webhook)) {
      Print("微信机器人初始化失败，请检查Webhook地址");
      return(INIT_FAILED);
   }
   
   // 英文测试
   // string enMessage = "WebhookTest";
   // if(!wechatManager.SendMessage(enMessage)) {
   //    Print("英文消息测试失败");
   //    return(INIT_FAILED);
   // }
   Sleep(1000);
   
   // 中文测试
   string cnMessage = "微信接口中文测试@" + TimeToString(TimeLocal(),TIME_DATE|TIME_SECONDS);
   if(!wechatManager.SendMessage(cnMessage)) {
      Print("中文消息测试失败");
      return(INIT_FAILED);
   }
   
   Print("中文消息测试成功");
   return(INIT_SUCCEEDED);
}
//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   // 清理操作
}
//+------------------------------------------------------------------+
//| Expert tick function                                            |
//+------------------------------------------------------------------+
void OnTick()
{
   // 无需实现
}
//+------------------------------------------------------------------+