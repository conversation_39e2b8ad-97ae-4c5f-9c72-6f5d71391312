#include <Trade\Trade.mqh>
#include <Trade\PositionInfo.mqh>
#include <Trade\OrderInfo.mqh>

CTrade trade; // 交易操作对象，用于执行买卖操作
CPositionInfo positionInfo; // 持仓信息对象，用于获取当前持仓信息
COrderInfo orderInfo; // 订单信息对象，用于获取订单详细信息

string tradeComment = __FILE__; // 交易注释，默认使用当前文件名

// 矩形管理相关变量
string rectangleNames[]; // 存储矩形对象名称的数组
int rectangleCount = 0; // 当前矩形数量

input group "基本参数"; // 输入参数分组，包含策略基本配置
input int inputMagicNum = 888888; // 魔术号，用于标识EA的订单
input ENUM_TIMEFRAMES PERIOD = PERIOD_H4; // 策略使用的时间周期，默认H4
input int inputNarrowRangeCount = 4; // 窄幅K线数量，默认4根
input double inputLotSize = 1; // 交易手数，默认1手
input double inputStopLoss = 100; // 止损点数，默认100点
input double inputTakeProfit = 100; // 止盈点数，默认100点

input group "显示参数"; // 显示相关参数
input bool inputShowRectangles = true; // 是否显示矩形标记
input color inputRectangleColor = clrYellow; // 矩形颜色
input int inputMaxRectangles = 50; // 最大矩形数量

int OnInit(){
    trade.SetExpertMagicNumber(inputMagicNum);

    // 初始化矩形数组
    ArrayResize(rectangleNames, inputMaxRectangles);
    rectangleCount = 0;

    return(INIT_SUCCEEDED);
}

void OnDeinit(const int reason){
    // 清理所有矩形对象
    clearAllRectangles();
}

void OnTick(){
    if(!isNewBar(_Symbol, PERIOD)) return;

    int signal = hasSignal();

    if(signal == 1){
        closeAllPositions(_Symbol, inputMagicNum);

        double high = iHigh(_Symbol, PERIOD, 1);
        double low = iLow(_Symbol, PERIOD, 1);

        datetime expiration = iTime(_Symbol, PERIOD,0)+1*PeriodSeconds(PERIOD);

        double buySl = inputStopLoss   == 0 ? 0 : high - inputStopLoss * _Point;
        double buyTp = inputTakeProfit == 0 ? 0 : high + inputTakeProfit * _Point;
        trade.BuyStop(inputLotSize, high, _Symbol, buySl, buyTp, ORDER_TIME_SPECIFIED, expiration, tradeComment);

        double sellSl = inputStopLoss   == 0 ? 0 : low + inputStopLoss * _Point;
        double sellTp = inputTakeProfit == 0 ? 0 : low - inputTakeProfit * _Point;
        trade.SellStop(inputLotSize, low, _Symbol, sellSl, sellTp, ORDER_TIME_SPECIFIED, expiration, tradeComment);

        // 绘制矩形标记NR4形态
        if(inputShowRectangles){
            drawNR4Rectangle();
        }
    }
}

/*
开仓信号：
1：当前K的振幅比前面三根都小
2：当前K的最高点小于前一根的高点，最低点大于前一根的低点
3：高低点挂突破单
*/
int hasSignal(){
    double high1 = iHigh(_Symbol, PERIOD, 1);
    double low1 = iLow(_Symbol, PERIOD, 1);

    double high2 = iHigh(_Symbol, PERIOD, 2);
    double low2 = iLow(_Symbol, PERIOD, 2);

// 孕线形态，满足条件2
    if(high1>high2 && low1<low2) return 0;

// 满足条件1
    for(int i=1;i<=inputNarrowRangeCount;i++){
        double high = iHigh(_Symbol, PERIOD, 1+i);
        double low = iLow(_Symbol, PERIOD, 1+i);

        if((high1-low1)>(high-low)) return 0;
    }

    return 1;
}


void deleteAllOrders(string symbol, long magicNum){
    for(int i = OrdersTotal() - 1; i >= 0; i--){
        if(orderInfo.SelectByIndex(i)){
            if(orderInfo.Symbol() == symbol && orderInfo.Magic() == magicNum){
                bool result = trade.OrderDelete(orderInfo.Ticket());
                if(!result) Print(symbol, "|",magicNum, " 撤单失败. Return code=", trade.ResultRetcode(),
                ". Code description: ", trade.ResultRetcodeDescription());
            }
        }
    }

}

void closeAllPositions(string symbol, long magicNum){
    for(int i = PositionsTotal() - 1; i >= 0; i++){
        if(positionInfo.SelectByIndex(i)){
            if(positionInfo.Symbol() == symbol && positionInfo.Magic() == magicNum){
                bool result = trade.PositionClose(positionInfo.Ticket());
                if(!result) Print(symbol, "|",magicNum, " 平仓失败. Return code=", trade.ResultRetcode(),
                ". Code description: ", trade.ResultRetcodeDescription());
            }
        }
    }
}

bool isNewBar(string symbol, ENUM_TIMEFRAMES period){
    datetime currentBarTime = iTime(symbol, period, 0);
    static datetime prevBarTime = currentBarTime;
    if(prevBarTime < currentBarTime){
        prevBarTime = currentBarTime;
        return true;
    }
    return false;
}

// 绘制NR4矩形标记
void drawNR4Rectangle(){
    // 计算矩形的时间和价格范围
    datetime time1 = iTime(_Symbol, PERIOD, inputNarrowRangeCount); // 最早的K线时间
    datetime time2 = iTime(_Symbol, PERIOD, 1); // 当前信号K线时间

    // 计算价格范围（包含所有相关K线的高低点）
    double highestPrice = 0;
    double lowestPrice = DBL_MAX;

    for(int i = 1; i <= inputNarrowRangeCount; i++){
        double high = iHigh(_Symbol, PERIOD, i);
        double low = iLow(_Symbol, PERIOD, i);

        if(high > highestPrice) highestPrice = high;
        if(low < lowestPrice) lowestPrice = low;
    }

    // 生成唯一的矩形名称
    string rectName = "NR4_Rect_" + IntegerToString(GetTickCount());

    // 创建矩形对象
    if(ObjectCreate(0, rectName, OBJ_RECTANGLE, 0, time1, lowestPrice, time2, highestPrice)){
        ObjectSetInteger(0, rectName, OBJPROP_COLOR, inputRectangleColor);
        ObjectSetInteger(0, rectName, OBJPROP_STYLE, STYLE_SOLID);
        ObjectSetInteger(0, rectName, OBJPROP_WIDTH, 1);
        ObjectSetInteger(0, rectName, OBJPROP_FILL, false);
        ObjectSetInteger(0, rectName, OBJPROP_BACK, false);
        ObjectSetString(0, rectName, OBJPROP_TOOLTIP, "NR" + IntegerToString(inputNarrowRangeCount) + " 形态");

        // 管理矩形数量
        addRectangleToArray(rectName);
    }
}

// 添加矩形到管理数组
void addRectangleToArray(string rectName){
    if(rectangleCount >= inputMaxRectangles){
        // 删除最旧的矩形
        if(rectangleNames[0] != ""){
            ObjectDelete(0, rectangleNames[0]);
        }

        // 数组元素前移
        for(int i = 0; i < inputMaxRectangles - 1; i++){
            rectangleNames[i] = rectangleNames[i + 1];
        }
        rectangleNames[inputMaxRectangles - 1] = rectName;
    }
    else{
        rectangleNames[rectangleCount] = rectName;
        rectangleCount++;
    }
}

// 清理所有矩形
void clearAllRectangles(){
    for(int i = 0; i < rectangleCount; i++){
        if(rectangleNames[i] != ""){
            ObjectDelete(0, rectangleNames[i]);
        }
    }
    rectangleCount = 0;
}