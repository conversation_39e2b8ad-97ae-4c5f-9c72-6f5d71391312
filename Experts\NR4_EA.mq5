#include <Trade\Trade.mqh>
#include <Trade\PositionInfo.mqh>
#include <Trade\OrderInfo.mqh>

CTrade trade; // 交易操作对象，用于执行买卖操作
CPositionInfo positionInfo; // 持仓信息对象，用于获取当前持仓信息
COrderInfo orderInfo; // 订单信息对象，用于获取订单详细信息

string tradeComment = __FILE__; // 交易注释，默认使用当前文件名

input group "基本参数"; // 输入参数分组，包含策略基本配置
input int inputMagicNum = 888888; // 魔术号，用于标识EA的订单
input ENUM_TIMEFRAMES PERIOD = PERIOD_H4; // 策略使用的时间周期，默认H4
input int inputNarrowRangeCount = 4; // 窄幅K线数量，默认4根
input double inputLotSize = 1; // 交易手数，默认1手
input double inputStopLoss = 100; // 止损点数，默认100点
input double inputTakeProfit = 100; // 止盈点数，默认100点

int Oninit(){
    trade.SetExpertMagicNumber(inputMagicNum);
    return(INIT_SUCCEEDED);
}

void OnDeinit(const int reason){

}

void OnTick(){
    if(!isNewBar(_Symbol, PERIOD)) return;

    int signal = hasSignal();

    if(signal == 1){
        closeAllPositions(_Symbol, inputMagicNum);

        double high = iHigh(_Symbol, PERIOD, 1);
        double low = iLow(_Symbol, PERIOD, 1);

        datetime expiration = iTime(_Symbol, PERIOD,0)+1*PeriodSeconds(PERIOD);

        double buySl = inputStopLoss   == 0 ? 0 : high - inputStopLoss * _Point;
        double buyTp = inputTakeProfit == 0 ? 0 : high + inputTakeProfit * _Point;
        trade.BuyStop(inputLotSize, high, _Symbol, buySl, buyTp, ORDER_TIME_SPECIFIED, expiration, tradeComment);

        double sellSl = inputStopLoss   == 0 ? 0 : low + inputStopLoss * _Point;
        double sellTp = inputTakeProfit == 0 ? 0 : low - inputTakeProfit * _Point;
        trade.SellStop(inputLotSize, low, _Symbol, sellSl, sellTp, ORDER_TIME_SPECIFIED, expiration, tradeComment);

    }
}

/*
开仓信号：
1：当前K的振幅比前面三根都小
2：当前K的最高点小于前一根的高点，最低点大于前一根的低点
3：高低点挂突破单
*/
int hasSignal(){
    double high1 = iHigh(_Symbol, PERIOD, 1);
    double low1 = iLow(_Symbol, PERIOD, 1);

    double high2 = iHigh(_Symbol, PERIOD, 2);
    double low2 = iLow(_Symbol, PERIOD, 2);

// 孕线形态，满足条件2
    if(high1>high2 && low1<low2) return 0;

// 满足条件1
    for(int i=1;i<=inputNarrowRangeCount;i++){
        double high = iHigh(_Symbol, PERIOD, 1+1);
        double low = iLow(_Symbol, PERIOD, 1+1);

        if((high1-low1)>(high-low)) return 0;
    }

    return 1;
}


void deleteAllOrders(string symbol, long magicNum){
    for(int i = OrdersTotal() - 1; i >= 0; i--){
        if(orderInfo.SelectByIndex(i)){
            if(orderInfo.Symbol() == symbol && orderInfo.Magic() == magicNum){
                bool result = trade.OrderDelete(orderInfo.Ticket());
                if(!result) Print(symbol, "|",magicNum, " 撤单失败. Return code=", trade.ResultRetcode(),
                ". Code description: ", trade.ResultRetcodeDescription());
            }
        }
    }

}

void closeAllPositions(string symbol, long magicNum){
    for(int i = PositionsTotal() - 1; i >= 0; i++){
        if(positionInfo.SelectByIndex(i)){
            if(positionInfo.Symbol() == symbol && positionInfo.Magic() == magicNum){
                bool result = trade.PositionClose(positionInfo.Ticket());
                if(!result) Print(symbol, "|",magicNum, " 平仓失败. Return code=", trade.ResultRetcode(),
                ". Code description: ", trade.ResultRetcodeDescription());
            }
        }
    }
}

bool isNewBar(string symbol, ENUM_TIMEFRAMES period){
    datetime currentBarTime = iTime(symbol, period, 0);
    static datetime prevBarTime = currentBarTime;
    if(prevBarTime < currentBarTime){
        prevBarTime = currentBarTime;
        return true;
    }
    return false;
}