#property copyright "Copyright 2024, <PERSON>rae AI"
#property version   "1.20"
#property strict

//+------------------------------------------------------------------+
//| 震荡行情检测EA                                         |
//+------------------------------------------------------------------+

// 外部参数
input int ADX_Period = 14;               // ADX周期
input double ADX_Threshold = 25.0;       // ADX趋势阈值
input int BB_Period = 20;                // 布林带周期
input double BB_Deviation = 2.0;         // 标准差倍数
input int RSI_Period = 14;               // RSI周期
input double PriceFluctuation = 0.0025;  // 价格波动幅度(当前品种报价比例)
input ENUM_TIMEFRAMES TimeFrame = PERIOD_M15;

// 面板参数
input int PanelX = 20;
input int PanelY = 20;
input color RangeColor = clrGoldenrod;    // 震荡颜色
input color UpTrendColor = clrLime;      // 上升趋势颜色
input color DownTrendColor = clrRed;     // 下降趋势颜色

// 全局变量
int adxHandle, bbHandle, rsiHandle, bbWidthMAHandle, atrHandle;
double adxValues[], diPlus[], diMinus[], bbWidth[], bbWidthMA[], rsiValues[];
double bbUpper[], bbMiddle[], bbLower[]; // 布林带上中下轨数据
double atrValues[]; // ATR数据作为备选波动性指标
string marketCondition = "N/A";
long chartID;
datetime lastAlertTime;
int consolidationCount = 0, totalCount = 0;
bool isConsolidation;
bool useBBWidth = true; // 是否使用布林带宽度判断波动性

int OnInit()
{
   chartID = ChartID();
   CreateInfoPanel();

   // 初始化指标句柄
   adxHandle = iADX(_Symbol, TimeFrame, ADX_Period);
   bbHandle = iBands(_Symbol, TimeFrame, BB_Period, 0, BB_Deviation, PRICE_CLOSE);
   rsiHandle = iRSI(_Symbol, TimeFrame, RSI_Period, PRICE_CLOSE);
   bbWidthMAHandle = iMA(_Symbol, TimeFrame, BB_Period, 0, MODE_SMA, PRICE_CLOSE);
   atrHandle = iATR(_Symbol, TimeFrame, BB_Period); // 使用与布林带相同周期的ATR
   
   // 输出调试信息
   Print("指标初始化 - ADX句柄:", adxHandle, " BB句柄:", bbHandle, " RSI句柄:", rsiHandle, " ATR句柄:", atrHandle);
   Print("布林带缓冲区数量:", BarsCalculated(bbHandle));
   
   // 预热指标数据
   for(int i=0; i<10; i++) {
      double temp[];
      if(CopyBuffer(bbHandle, 0, 0, 1, temp) <= 0) {
         Print("预热布林带数据 - 尝试 ", i+1);
         Sleep(200);
      }
   }

   // 等待指标缓冲区准备就绪
   int waitTime = 0;
   while(!BarsCalculated(adxHandle) || !BarsCalculated(bbHandle) || 
         !BarsCalculated(rsiHandle) || !BarsCalculated(bbWidthMAHandle) || 
         !BarsCalculated(atrHandle))
   {
      Sleep(100);
      waitTime += 100;
      if(waitTime > 5000) // 最多等待5秒
      {
         Print("指标缓冲区准备超时");
         return INIT_FAILED;
      }
   }

   // 指标句柄验证
   if(adxHandle == INVALID_HANDLE || bbHandle == INVALID_HANDLE || rsiHandle == INVALID_HANDLE || bbWidthMAHandle == INVALID_HANDLE){
      Alert("指标初始化失败");
      return INIT_FAILED;
   }

   // 设置数组为时间序列并预分配空间
   ArraySetAsSeries(adxValues, true);
   ArraySetAsSeries(diPlus, true);
   ArraySetAsSeries(diMinus, true);
   ArraySetAsSeries(bbWidth, true);
   ArraySetAsSeries(bbWidthMA, true);
   ArraySetAsSeries(rsiValues, true);
   ArraySetAsSeries(bbUpper, true);
   ArraySetAsSeries(bbMiddle, true);
   ArraySetAsSeries(bbLower, true);
   ArraySetAsSeries(atrValues, true);
   
   // 预加载足够的历史数据
   int required_bars = 100; // 预加载100根K线数据
   for(int retry=0; retry<3; retry++) { // 增加重试机制
      if(InitializeIndicatorData(required_bars)) {
         Print("历史数据加载成功");
         return INIT_SUCCEEDED;
      }
      Sleep(1000); // 等待1秒后重试
   }
   Alert("历史数据加载失败");
   return INIT_FAILED;

   return INIT_SUCCEEDED;
}

// 初始化指标数据
bool InitializeIndicatorData(int bars)
{
   // 预分配数组空间
   ArrayResize(adxValues, bars);
   ArrayResize(diPlus, bars);
   ArrayResize(diMinus, bars);
   ArrayResize(bbWidth, bars);
   ArrayResize(bbWidthMA, bars);
   ArrayResize(rsiValues, bars);
   ArrayResize(bbUpper, bars);
   ArrayResize(bbMiddle, bars);
   ArrayResize(bbLower, bars);
   ArrayResize(atrValues, bars);
   
   // 等待指标缓冲区准备就绪
   int waitTime = 0;
   while(!BarsCalculated(adxHandle) || !BarsCalculated(bbHandle) || 
         !BarsCalculated(rsiHandle) || !BarsCalculated(bbWidthMAHandle) || 
         !BarsCalculated(atrHandle))
   {
      Sleep(100);
      waitTime += 100;
      if(waitTime > 5000) // 最多等待5秒
      {
         Print("指标缓冲区准备超时");
         return false;
      }
   }
   
   // 尝试加载历史数据
   for(int attempt=0; attempt<8; attempt++) // 增加到8次重试
   {
      bool success = true;
      
      // 输出当前尝试次数
      Print("尝试加载历史数据 - 第", attempt+1, "次尝试");
      
      // ADX指标数据
      if(CopyBuffer(adxHandle, 0, 0, bars, adxValues) != bars) {
         Print("ADX主线数据加载失败");
         success = false;
      }
      if(CopyBuffer(adxHandle, 1, 0, bars, diPlus) != bars) {
         Print("+DI数据加载失败");
         success = false;
      }
      if(CopyBuffer(adxHandle, 2, 0, bars, diMinus) != bars) {
         Print("-DI数据加载失败");
         success = false;
      }
      
      // 布林带数据 - 获取上中下轨
      if(CopyBuffer(bbHandle, 0, 0, bars, bbUpper) != bars) {
         Print("布林带上轨数据加载失败");
         success = false;
      }
      if(CopyBuffer(bbHandle, 1, 0, bars, bbMiddle) != bars) {
         Print("布林带中轨数据加载失败");
         success = false;
      }
      if(CopyBuffer(bbHandle, 2, 0, bars, bbLower) != bars) {
         Print("布林带下轨数据加载失败");
         success = false;
      }
      
      // 如果布林带数据加载成功，计算宽度
      if(success) {
         // 自定义计算布林带宽度
         for(int i=0; i<bars; i++) {
            // 计算布林带宽度 = (上轨 - 下轨) / 中轨
            bbWidth[i] = (bbUpper[i] - bbLower[i]) / bbMiddle[i];
         }
         Print("布林带宽度计算成功，样本值:", bbWidth[0]);
         useBBWidth = true;
      } else {
         Print("无法计算布林带宽度，数据不完整，将使用ATR替代");
         useBBWidth = false;
      }
      
      // 加载ATR数据作为备选
      if(CopyBuffer(atrHandle, 0, 0, bars, atrValues) != bars) {
         Print("ATR数据加载失败");
         success = false;
      } else {
         Print("ATR数据加载成功，样本值:", atrValues[0]);
      }
      
      // 布林带MA数据
      if(CopyBuffer(bbWidthMAHandle, 0, 0, bars, bbWidthMA) != bars) {
         Print("布林带MA数据加载失败");
         success = false;
      }
      
      // RSI数据
      if(CopyBuffer(rsiHandle, 0, 0, bars, rsiValues) != bars) {
         Print("RSI数据加载失败");
         success = false;
      }
      
      if(success) {
         Print("所有指标数据加载成功");
         return true;
      }
      
      // 增加等待时间，随着尝试次数增加而增加
      int wait_time = 500 * (attempt + 1); // 从500ms递增到4000ms
      Print("等待", wait_time, "毫秒后重试...");
      Sleep(wait_time);
   }
   
   Print("历史数据加载失败，已达到最大重试次数");
   return false;
}

void OnTick()
{
   // 获取指标数据，增加重试机制
   for(int attempt=0; attempt<3; attempt++) // 最多重试3次
   {
      bool dataComplete = 
         CopyBuffer(adxHandle, 0, 0, 3, adxValues) == 3 && 
         CopyBuffer(adxHandle, 1, 0, 3, diPlus) == 3 && 
         CopyBuffer(adxHandle, 2, 0, 3, diMinus) == 3 &&
         CopyBuffer(bbHandle, 0, 0, 3, bbUpper) == 3 && 
         CopyBuffer(bbHandle, 1, 0, 3, bbMiddle) == 3 && 
         CopyBuffer(bbHandle, 2, 0, 3, bbLower) == 3 && 
         CopyBuffer(bbWidthMAHandle, 0, 0, 3, bbWidthMA) == 3 &&
         CopyBuffer(rsiHandle, 0, 0, 3, rsiValues) == 3 &&
         CopyBuffer(atrHandle, 0, 0, 3, atrValues) == 3;
         
      if(dataComplete)
      {
         // 计算布林带宽度
         for(int i=0; i<3; i++) {
            if(bbMiddle[i] > 0) { // 防止除以零
               bbWidth[i] = (bbUpper[i] - bbLower[i]) / bbMiddle[i];
            }
         }
        //  Print("OnTick - 布林带宽度: ", bbWidth[0], " MA宽度: ", bbWidthMA[0]);
         break;
      }
      
      if(attempt == 2) { // 如果最后一次尝试仍然失败
         Print("指标数据获取不完整，已重试", IntegerToString(attempt+1), "次");
         marketCondition = "数据异常";
         UpdateInfoPanel();
         return;
      }
      Sleep(50); // 等待50毫秒后重试
   }
   
   // 布林带宽度已在数据获取阶段计算完成
   
   UpdateInfoPanel();

   // 计算市场状态
   isConsolidation = true;
   
   // ADX条件：主线低于阈值且+DI/-DI收敛
   bool adxCondition = adxValues[0] < ADX_Threshold && 
                      MathAbs(diPlus[0] - diMinus[0]) < (ADX_Threshold * 0.5);
   if(!adxCondition) isConsolidation = false;

   // 波动性过滤
   if(useBBWidth) {
      // 使用布林带宽度过滤（与MA指标对比）
      if(bbWidth[0] > bbWidthMA[0]){
         isConsolidation = false;
         Print("布林带宽度过滤: ", bbWidth[0], " > ", bbWidthMA[0]);
      }
   } else {
      // 使用ATR过滤（与平均值对比）
      double avgATR = (atrValues[0] + atrValues[1] + atrValues[2]) / 3;
      if(atrValues[0] > avgATR * 1.2) { // ATR高于平均值的20%
         isConsolidation = false;
         Print("ATR过滤: ", atrValues[0], " > ", avgATR * 1.2);
      }
   }

   // RSI中性区域过滤
   if(rsiValues[0] < 40 || rsiValues[0] > 60){
      isConsolidation = false;
   }

   // 价格波动幅度过滤
   double currentRange = (iHigh(_Symbol,TimeFrame,0) - iLow(_Symbol,TimeFrame,0)) / iClose(_Symbol,TimeFrame,0);
   if(currentRange > PriceFluctuation){
      isConsolidation = false;
   }

   if(isConsolidation){
   marketCondition = "震荡行情";
}else{
   bool isUpTrend = diPlus[0] > diMinus[0];
   marketCondition = isUpTrend ? "▲上升趋势" : "▼下降趋势";
}
   UpdateInfoPanel();

   // 触发警报
   if((TimeCurrent() - lastAlertTime) > 3600){
      if(isConsolidation){
         Alert("进入震荡区间 ", Symbol(), " M15");
      } else {
         Alert("趋势形成 ", (diPlus[0] > diMinus[0] ? "▲" : "▼"), Symbol(), " M15");
      }
      lastAlertTime = TimeCurrent();
   }
}

// EA的面板创建函数
void CreateInfoPanel()
{
   // 背景矩形
   ObjectCreate(chartID, "InfoPanelBG", OBJ_RECTANGLE_LABEL, 0, 0, 0);
   ObjectSetInteger(chartID, "InfoPanelBG", OBJPROP_XDISTANCE, PanelX);
   ObjectSetInteger(chartID, "InfoPanelBG", OBJPROP_YDISTANCE, PanelY);
   ObjectSetInteger(chartID, "InfoPanelBG", OBJPROP_XSIZE, 300);
   ObjectSetInteger(chartID, "InfoPanelBG", OBJPROP_YSIZE, 110);

   // BB宽度标签
   ObjectCreate(chartID, "BBWidthLabel", OBJ_LABEL, 0, 0, 0);
   ObjectSetInteger(chartID, "BBWidthLabel", OBJPROP_XDISTANCE, PanelX+10);
   ObjectSetInteger(chartID, "BBWidthLabel", OBJPROP_YDISTANCE, PanelY+70);
   ObjectSetInteger(chartID, "BBWidthLabel", OBJPROP_FONTSIZE, 8);
   ObjectSetInteger(chartID, "BBWidthLabel", OBJPROP_COLOR, clrWhite);

   // 历史统计标签
   ObjectCreate(chartID, "ConsolidationStats", OBJ_LABEL, 0, 0, 0);
   ObjectSetInteger(chartID, "ConsolidationStats", OBJPROP_XDISTANCE, PanelX+10);
   ObjectSetInteger(chartID, "ConsolidationStats", OBJPROP_YDISTANCE, PanelY+40);
   ObjectSetInteger(chartID, "ConsolidationStats", OBJPROP_FONTSIZE, 8);
   ObjectSetInteger(chartID, "ConsolidationStats", OBJPROP_COLOR, clrWhite);
   
   // 状态标签
   ObjectCreate(chartID, "MarketStatus", OBJ_LABEL, 0, 0, 0);
   ObjectSetInteger(chartID, "MarketStatus", OBJPROP_XDISTANCE, PanelX+10);
   ObjectSetInteger(chartID, "MarketStatus", OBJPROP_YDISTANCE, PanelY+10);
   ObjectSetInteger(chartID, "MarketStatus", OBJPROP_FONTSIZE, 8);
   ObjectSetInteger(chartID, "MarketStatus", OBJPROP_COLOR, clrWhite);
}

// 更新面板显示
void UpdateInfoPanel()
{
   color panelColor = RangeColor;
if(marketCondition == "▲上升趋势") panelColor = UpTrendColor;
if(marketCondition == "▼下降趋势") panelColor = DownTrendColor;
   color textColor = clrWhite; // 固定使用白色文字
   
   ObjectSetInteger(chartID, "InfoPanelBG", OBJPROP_BGCOLOR, panelColor);
   // 更新统计信息
   totalCount++;
   if(isConsolidation) consolidationCount++;
   double ratio = (totalCount > 0) ? (consolidationCount*100.0/totalCount) : 0;

   ObjectSetString(chartID, "MarketStatus", OBJPROP_TEXT, "市场状态：" + marketCondition);
   ObjectSetString(chartID, "ConsolidationStats", OBJPROP_TEXT, "震荡比例：" + DoubleToString(ratio,1) + "%");
   
   // 根据使用的波动性指标类型显示不同信息
   if(useBBWidth) {
      // 显示BB宽度（放大10000倍）
      ObjectSetString(chartID, "BBWidthLabel", OBJPROP_TEXT, "BB宽度：" + DoubleToString(bbWidth[0]*10000,4));
   } else {
      ObjectSetString(chartID, "BBWidthLabel", OBJPROP_TEXT, "ATR值：" + DoubleToString(atrValues[0],5));
   }
   
   ObjectSetInteger(chartID, "MarketStatus", OBJPROP_COLOR, textColor);
   ObjectSetInteger(chartID, "ConsolidationStats", OBJPROP_COLOR, textColor);
   ObjectSetInteger(chartID, "BBWidthLabel", OBJPROP_COLOR, textColor);
}

void OnDeinit(const int reason)
{
   // 删除所有创建的图表对象
   ObjectDelete(chartID, "InfoPanelBG");
   ObjectDelete(chartID, "MarketStatus");
   ObjectDelete(chartID, "ConsolidationStats");
   ObjectDelete(chartID, "BBWidthLabel");
   
   // 释放所有指标句柄
   if(adxHandle != INVALID_HANDLE) IndicatorRelease(adxHandle);
   if(bbHandle != INVALID_HANDLE) IndicatorRelease(bbHandle);
   if(rsiHandle != INVALID_HANDLE) IndicatorRelease(rsiHandle);
   if(bbWidthMAHandle != INVALID_HANDLE) IndicatorRelease(bbWidthMAHandle);
   if(atrHandle != INVALID_HANDLE) IndicatorRelease(atrHandle);
   
   Print("EA已卸载，所有资源已释放");
}